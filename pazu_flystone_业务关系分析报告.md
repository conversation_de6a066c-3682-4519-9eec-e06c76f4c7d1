# Pazu 和 Flystone 项目业务关系分析报告

## 1. 项目概述

### 1.1 项目定位
- **Pazu**：船舶端数据传输中间件，负责将船上各类数据通过UDP协议传输到岸上系统
- **Flystone**：岸上端数据接收处理系统，负责接收船舶传输的数据并进行后续处理

### 1.2 项目特点
- **Pazu**：专为海上船舶与岸上系统间通信设计，解决船舶在海上网络环境不稳定情况下的数据可靠传输问题
- **Flystone**：岸上数据接收和处理中心，具备数据验证、修复、转发等功能

## 2. 项目架构关系

### 2.1 整体架构
两个项目形成了典型的**客户端-服务端**架构关系：
- Pazu 作为数据发送端（船舶端）
- Flystone 作为数据接收端（岸上端）
- 通过 UDP 协议进行点对点通信

### 2.2 网络配置
```yaml
# Pazu 配置 - 岸上UDP接收配置
laputa:
  ip: 127.0.0.1
  port: 28001

# Pazu 配置 - 船上UDP接收配置  
pazu:
  sn: aa0001
  port1: 10132
  port2: 10133

# Flystone 配置 - 岸上UDP接收配置
flystone:
  udpPort: 10003
```

### 2.3 依赖关系
- **直接依赖**：Pazu 直接向 Flystone 发送数据
- **间接依赖**：两者都依赖 Redis 和 Kafka 进行数据缓存和消息传递
- **共享组件**：使用相同的 `TransferPackage` 数据结构和 `dctcore` 公共模块

## 3. 业务功能关联

### 3.1 Pazu 的核心职责
1. **数据收集**：通过 Kafka 消费者接收船上各类数据
   - 设备分析数据（ship_device_analysis_data_topic）
   - 快照数据（ship_snapshot_data_topic）
   - 同步数据（ship_sync_data_topic）
   - 原始数据（ship_raw_data_topic）

2. **数据传输**：将收集的数据通过 UDP 发送到岸上系统

3. **传输优化**：实现优先级队列、带宽控制、数据修复等机制

### 3.2 Flystone 的核心职责
1. **数据接收**：通过 UDP 服务器接收船舶传输的数据
2. **数据处理**：根据数据类型进行分类处理
3. **数据转发**：将处理后的数据发送到 Kafka 供其他系统消费
4. **数据修复**：处理数据丢失和补发请求

### 3.3 业务流程协同
```mermaid
flowchart TD
    A[船上设备数据] --> B[Kafka Topics]
    B --> C[Pazu 消费者]
    C --> D[Redis 队列]
    D --> E[UDP 发送服务]
    E --> F[网络传输]
    F --> G[Flystone UDP 服务器]
    G --> H[数据处理服务]
    H --> I[Kafka 转发]
    I --> J[Laputa 等下游系统]
```

## 4. 数据流向分析

### 4.1 数据传输协议
两个系统使用自定义的 `TransferPackage` 数据结构进行通信：

```java
public class TransferPackage {
    private String sn;           // 船只序列号
    private Integer commandNum;  // 统一编码
    private Integer packageType; // 包类型
    private String message;      // 包数据
    private Integer deviceType;  // 设备类型
    private String deviceCode;   // 设备编号
    // ... 其他字段
}
```

### 4.2 数据类型分类
| 包类型 | 数值 | 描述 | 处理方式 |
|--------|------|------|----------|
| DEVICE_DATA | 0 | 传感器设备数据 | 直接转发到Kafka |
| SNAPSHOT_DATA | 1 | 图片快照数据 | 拆包重组后保存 |
| CONNECT_KEEP | 2 | UDP信号维持 | 连接保活处理 |
| DATA_SYNC | 3 | 数据同步 | 同步到数据库 |
| 原始数据 | 5 | 原始设备数据 | 转发处理 |

### 4.3 优先级处理机制
Pazu 实现了基于优先级的数据发送队列：
- **优先级 10**：连接保活、同步确认（最高优先级）
- **优先级 20**：设备分析数据
- **优先级 30**：设备数据修复
- **优先级 40**：快照数据和快照数据修复
- **优先级 50**：低优先级数据

## 5. 技术栈对比

### 5.1 相同技术栈
| 技术组件 | Pazu | Flystone | 用途 |
|---------|------|----------|------|
| Spring Boot | 2.3.0 | 2.3.0 | 应用框架 |
| Redis | ✓ | ✓ | 数据缓存和队列 |
| Kafka | ✓ | ✓ | 消息队列 |
| Netty | ✓ | ✓ | UDP 通信 |
| OpenFeign | ✓ | ✓ | 服务间调用 |

### 5.2 差异化技术栈
| 技术组件 | Pazu | Flystone | 说明 |
|---------|------|----------|------|
| 数据库 | ✗ | MySQL | Flystone 需要持久化存储 |
| JSON 解析 | FastJSON | Jackson | 不同的 JSON 处理库 |
| 文件存储 | ✗ | 本地文件系统 | Flystone 需要存储快照文件 |

## 6. 数据丢失与补发机制

### 6.1 数据丢失检测机制

#### Flystone 端的丢失检测算法
使用**序列号连续性检测**来发现数据丢失：

1. **间隔检测**：当接收到的最大序列号与已检查的最大序列号差值超过100时，触发检测
2. **序列扫描**：逐个检查序列号是否存在于Redis中
3. **丢失记录**：将缺失的序列号记录到丢失数据列表中

```java
private void checkDataComplete(Integer maxReceiveNum, Integer maxCheckNum, TransferPackage transferPackage) {
    // 记录的最大编号超过已检查编号一定数量之后，开始检查是否有丢数据
    int intervalNum = valueOperations.get(RedisKeyConstants.CHECK_INTERVAL_NUM) == null ? 100 : (Integer) valueOperations.get(RedisKeyConstants.CHECK_INTERVAL_NUM);
    if (maxReceiveNum - maxCheckNum < intervalNum) {
        return;
    }
    // 启动检查线程
}
```

### 6.2 补发请求机制

#### 补发请求协议
补发请求使用特定的协议格式：
- **类型标识**：第一个字节表示请求类型（"0"、"1"、"2"）
- **数据内容**：后续字节包含具体的补发信息

| 类型 | 描述 | 数据格式 |
|------|------|----------|
| 0 | 设备数据补发请求 | 4字节整数数组（丢失的序列号列表） |
| 1 | 快照数据补发请求 | 字符串格式（commandNum,channelCode,缺失的分片编号） |
| 2 | 同步数据请求 | JSON字符串 |

#### Pazu 端的补发处理
```java
@Override
public void channelRead(ChannelHandlerContext ctx, Object msg) {
    // 根据类型分发处理
    if ("0".equals(type)) {
        // 类型0：数据补充请求
        List<Integer> commandNumList = NumUtils.byteArray2List(contentByte);
        repairService.repairData(commandNumList);
    } else if ("1".equals(type)) {
        // 类型1：数据补包请求
        String repairMessage = new String(contentByte, StandardCharsets.UTF_8);
        repairService.repairPackage(repairMessage);
    }
}
```

### 6.3 数据缓存机制
- **Pazu**：发送数据时缓存到Redis，过期时间2天
- **Flystone**：接收数据时记录状态，过期时间3天
- **防重复**：使用Redis Set记录待补发的序列号

### 6.4 补发机制的关键特性
1. **防重复机制**：避免重复补发和重复处理
2. **数据有效性处理**：对不存在的数据发送特殊标记
3. **批量处理优化**：每次最多处理240个丢失序列号
4. **超时重试机制**：关键数据实现自动重试

### 6.5 数据丢失与补发详细流程

#### 6.5.1 正常数据传输流程
```mermaid
sequenceDiagram
    participant P as Pazu
    participant R1 as Pazu Redis
    participant N as Network
    participant F as Flystone
    participant R2 as Flystone Redis

    P->>R1: 1. 从Kafka接收数据
    P->>R1: 2. 存入发送队列(按优先级)
    P->>R1: 3. 缓存数据到Hash表(2天过期)
    P->>N: 4. UDP发送数据包
    N->>F: 5. 数据包到达Flystone
    F->>R2: 6. 记录接收状态(3天过期)
    F->>R2: 7. 更新最大接收序列号
    F->>F: 8. 转发到Kafka
```

#### 6.5.2 数据丢失检测流程
```mermaid
sequenceDiagram
    participant F as Flystone
    participant R as Flystone Redis
    participant T as 检测线程

    F->>R: 1. 接收新数据包(序列号N)
    F->>R: 2. 更新最大接收序列号
    F->>F: 3. 检查: maxReceiveNum - maxCheckNum >= 100?
    alt 需要检测
        F->>T: 4. 启动检测线程
        T->>R: 5. 获取检测锁
        loop 序列号扫描
            T->>R: 6. 检查序列号是否存在
            alt 序列号缺失
                T->>R: 7. 添加到丢失列表
            end
        end
        T->>R: 8. 更新最大检查序列号
        T->>T: 9. 触发补发请求
        T->>R: 10. 释放检测锁
    end
```

#### 6.5.3 设备数据补发流程
```mermaid
sequenceDiagram
    participant F as Flystone
    participant R2 as Flystone Redis
    participant N as Network
    participant P as Pazu
    participant R1 as Pazu Redis

    F->>R2: 1. 从丢失列表获取序列号(最多240个)
    F->>F: 2. 将序列号转换为字节数组
    F->>N: 3. 发送UDP补发请求(类型0 + 字节数组)
    N->>P: 4. Pazu接收补发请求
    P->>P: 5. 解析请求类型和序列号列表

    loop 处理每个序列号
        P->>R1: 6. 从Hash表查找缓存数据
        alt 数据存在
            P->>R1: 7. 标记为补发数据(isRepair=1)
            P->>R1: 8. 添加到补发队列
        else 数据不存在
            P->>R1: 9. 创建无效数据标记(deviceType=-1)
            P->>R1: 10. 添加到补发队列
        end
    end

    P->>N: 11. 按优先级30发送补发数据
    N->>F: 12. Flystone接收补发数据

    alt 有效数据
        F->>R2: 13. 记录接收状态
        F->>R2: 14. 从丢失列表移除序列号
        F->>F: 15. 转发到Kafka
    else 无效数据
        F->>R2: 16. 从丢失列表移除序列号
        F->>F: 17. 记录无效数据日志
    end
```

#### 6.5.4 快照数据补发流程
```mermaid
sequenceDiagram
    participant F as Flystone
    participant R2 as Flystone Redis
    participant N as Network
    participant P as Pazu
    participant R1 as Pazu Redis

    F->>F: 1. 检测快照数据完整性
    F->>F: 2. 发现缺失分片
    F->>F: 3. 构造补发请求字符串
    Note over F: 格式: commandNum,channelCode,分片1,分片2,...
    F->>N: 4. 发送UDP补发请求(类型1 + 字符串)
    N->>P: 5. Pazu接收补发请求
    P->>P: 6. 解析请求字符串

    alt 包含SUCCESS标记
        P->>R1: 7. 移除传输标记
        P->>P: 8. 检查是否需要发送新快照
    else 补发请求
        loop 处理每个分片
            P->>R1: 9. 从Hash表查找分片数据
            alt 分片存在
                P->>R1: 10. 标记为补发数据(isRepair=1)
                P->>R1: 11. 添加到补发队列
            end
        end
        P->>N: 12. 按优先级40发送补发分片
    end

    N->>F: 13. Flystone接收补发分片
    F->>F: 14. 重新检查完整性
    alt 快照完整
        F->>F: 15. 合并分片生成完整快照
        F->>F: 16. 保存到文件系统
        F->>N: 17. 发送SUCCESS确认
        F->>F: 18. 转发到Kafka
    end
```

#### 6.5.5 异常处理流程
```mermaid
flowchart TD
    A[数据传输异常] --> B{异常类型}

    B -->|网络超时| C[重试机制]
    C --> C1[等待8秒]
    C1 --> C2[检查确认状态]
    C2 --> C3{收到确认?}
    C3 -->|否| C4[重新发送]
    C3 -->|是| C5[传输成功]

    B -->|数据过期| D[清理机制]
    D --> D1[检查缓存过期时间]
    D1 --> D2[清理过期数据]
    D2 --> D3[更新状态记录]

    B -->|连接断开| E[重连机制]
    E --> E1[连接保活检测]
    E1 --> E2[重建UDP连接]
    E2 --> E3[恢复数据传输]

    B -->|存储空间不足| F[空间清理]
    F --> F1[检查磁盘使用率]
    F1 --> F2{使用率>90%?}
    F2 -->|是| F3[清理历史数据]
    F2 -->|否| F4[继续正常运行]

    B -->|数据损坏| G[数据验证]
    G --> G1[校验数据完整性]
    G1 --> G2{数据有效?}
    G2 -->|否| G3[请求重传]
    G2 -->|是| G4[正常处理]
```

#### 6.5.6 关键时间节点和参数
| 参数 | 数值 | 说明 |
|------|------|------|
| 检测间隔阈值 | 100 | 触发丢失检测的序列号差值 |
| 批量补发数量 | 240 | 每次最多补发的序列号数量 |
| 数据缓存时间(Pazu) | 2天 | 发送端数据缓存过期时间 |
| 状态记录时间(Flystone) | 3天 | 接收端状态记录过期时间 |
| 同步重试间隔 | 8秒 | 同步数据未确认时的重试间隔 |
| 快照补发重试间隔 | 15秒 | 快照数据补发的重试间隔 |
| 连接保活间隔 | 10秒 | UDP连接保活消息发送间隔 |
| 快照超时阈值 | 30分钟 | 快照传输超时判断阈值 |

## 7. 部署和运维关系

### 7.1 部署环境
- **Pazu**：部署在船舶端，端口 9997
- **Flystone**：部署在岸上端，端口 9993

### 7.2 监控和维护
- 两个系统都使用 Redis 进行状态监控
- 通过日志记录传输状态和异常信息
- 实现了连接保活机制确保通信稳定

### 7.3 系统集成
- **与 Sheeta 集成**：设备配置管理
- **与 Duffy 集成**：数据同步
- **与 Laputa 集成**：岸上数据处理

## 8. 总结

Pazu 和 Flystone 构成了一个完整的船岸数据传输系统：

### 8.1 系统特点
1. **互补关系**：Pazu 专注于数据发送和传输优化，Flystone 专注于数据接收和后续处理
2. **技术协同**：两者使用相同的核心技术栈，确保兼容性和一致性
3. **业务闭环**：从船上数据收集到岸上数据处理，形成完整的业务流程
4. **可靠性保障**：通过数据缓存、修复机制、优先级队列等确保数据传输的可靠性

### 8.2 核心优势
1. **高可靠性**：通过序列号连续性检测和自动补发机制，确保数据传输的完整性
2. **高效性**：优先级队列和批量处理优化，提高传输效率
3. **容错性**：多种异常处理机制，适应海上复杂网络环境
4. **可扩展性**：模块化设计，支持多种数据类型和传输模式

### 8.3 技术创新点
1. **自适应补发机制**：根据网络状况动态调整检测间隔和补发策略
2. **分层数据处理**：不同类型数据采用不同的传输和恢复策略
3. **状态一致性保障**：通过Redis缓存和时间戳机制确保数据状态一致性
4. **智能重试策略**：基于数据重要性和网络状况的智能重试机制

这种设计很好地解决了海上网络环境不稳定情况下的数据可靠传输问题，是一个典型的分布式数据传输解决方案。通过详细的流程设计和完善的异常处理机制，实现了高效、可靠的船岸数据传输系统，为海上作业提供了强有力的数据通信保障。
